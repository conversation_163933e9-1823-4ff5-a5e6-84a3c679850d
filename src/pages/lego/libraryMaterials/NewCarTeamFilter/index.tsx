import { BLMOrgBtCarTeam } from '@blmcp/peento-businessComponents';
import { useMemo, useRef, useEffect } from 'react';
import { useLegoReport } from '@blm/bi-lego-sdk/dist/es/utils';
import { useDispatch } from '@/utils/store';
import FilterWrapper from '../wrapper/FilterWrapper';
import stateContext, { StateProps } from '../../context/filterContext';

interface CityFilterProps {
  __id?: string; // 预览模式
  componentId?: string; // 编辑模式
  // 数据源
  kind: 'link' | 'authOpen';
  uuid: string;
}

// 关联组件参数与 setter 组件
export const LinkSetterComponent = {
  // 是否隐藏
  hidden: {
    componentName: 'HiddenSetter',
    // 该组件其他配置项
    props: {},
  },
};

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 4,
  // 不参与获取数据集相关事情
  notQueryData: true,
};

export const NewCarTeamFilter = function (props: CityFilterProps) {
  const context = stateContext(props.uuid);
  const [state, dispatch] = useDispatch<StateProps>(context);
  const ref = useRef(null);
  const [reportMeta] = useLegoReport(props.uuid);

  const kind = useMemo(() => {
    return (reportMeta.legoMinAuthLevel ?? 4) > 2 ? 1 : 2;
  }, [reportMeta]);

  useEffect(() => {
    dispatch({
      type: 'clear',
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [kind]);

  return (
    <FilterWrapper
      storeField="carTeam"
      componentProps={props}
      fieldProps={useMemo(() => {
        return {
          key: 'car_team_id',
          columnId: *********,
          dataType: 0,
        };
      }, [])}
      handleFieldLabel={(val) => {
        return val.length ? ref.current?.getSelectedLabelName?.() : [];
        // return (val.length ? ref.current?.getSelectedOptions?.() : [])
        //   .filter((f) => !f.cityHalfAuthFlag)
        //   .map((m) => m.transportCompanyName);
      }}
      transportCompanyName
    >
      <BLMOrgBtCarTeam
        ref={ref}
        multiple
        tenantId={state.tenant}
        authType={kind} // 有关联
        deleteFlag={2} // 正常+删除
        adcodeList={state.city}
        openUaFlag
        maxLength={400}
        // showAll={true}
      />
    </FilterWrapper>
  );
};
