/**
 * 日期组件
 */
import React, { useEffect, useState, useRef, useCallback } from 'react';
import dayjs from 'dayjs';
// import { DatePickerFilter as DatePickerFilterView } from '../../components/Filter/DatePickerFilter';
import { DateRangePicker } from '@blmcp/web-ui';
import relationCenterExp from '@/pages/lego/libraryMaterials/module/RelationCenter';
import useComponent from '@/pages/lego/hooks/useComponent';
import { getOtherPresets } from '../../components/Filter/DatePickerFilter/tools';
import FilterWrapper from '../wrapper/FilterWrapper';
import isMobile from '../../utils/isMobile';
import { DEFAULT_DATE_RANGE } from './constants/dateRangeOptions';

interface DatePickerFilterProps {
  title?: string;
  dataSetConfig: any;
  __id: any;
  componentId: any;
  defaultValue: any;
  dateRange?: number;
  uuid: string;
}

// 关联组件参数与 setter 组件
export const LinkSetterComponent = {
  dataSetConfig: {
    // 用到的组件
    componentName: 'AnalysisConfig',
    // 该组件其他配置项
    props: {
      list: [
        {
          label: '维度',
          key: 'dimensionInfo',
          onlyOne: true,
          ignoreSetDefaultComputeType: true,
          placeholder: '仅支持拖入 1 个字段',
        },
      ],
      dateOnly: true,
      indexDisabled: true,
    },
  },
  // 默认时间
  defaultValue: {
    componentName: 'DatePickerDefaultValue',
    // 该组件其他配置项
    props: {
      // 拖拽进入的，还需要配置数据集
      type: 'drop-component',
    },
  },
  // 是否隐藏
  hidden: {
    componentName: 'HiddenSetter',
    // 该组件其他配置项
    props: {
      noPadding: true,
    },
  },
  // 可选范围
  dateRange: {
    componentName: 'DatePickerRange',
    // 该组件其他配置项
    props: {},
  },
};

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 4,
  dataType: 1,
};

const temp_dateMap: any = {};
setTimeout(() => {
  const { project } = window.AliLowCodeEngine || {};
  // 暂时通过监听历史变化，重置缓存，后面统一处理默认值问题
  project?.currentDocument?.history.onChangeCursor(() => {
    // eslint-disable-next-line guard-for-in
    for (let key in temp_dateMap) {
      delete temp_dateMap[key];
    }
  });
});

// 逻辑层
export const DatePickerFilter = ({
  dataSetConfig,
  __id,
  componentId,
  defaultValue,
  dateRange = DEFAULT_DATE_RANGE,
  uuid,
}: DatePickerFilterProps) => {
  // 重置强制刷新用
  const [key, setKey] = useState(() => Date.now());

  const relationCenter = relationCenterExp(uuid);
  const defaultValueRef = useRef(defaultValue);
  const dateRangeRef = useRef(dateRange);
  const filterId = __id ?? componentId ?? '';
  const { dimensionInfo } = dataSetConfig ?? {};
  const dimension = dimensionInfo?.[0];
  const presets = getOtherPresets();
  const presetList = getOtherPresets();
  const [_, setMeta] = useComponent(filterId);

  useEffect(() => {
    setMeta(
      {
        query() {
          relationCenter.notify('all');
        },
      },
      true,
    );
  }, []);

  const getDefaultDateItem = useCallback(() => {
    const dateType = defaultValue?.dateType;
    // 外部自定义默认时间
    if (dateType === 'customDate') {
      // 如果 defaultValue 中有自定义的 value，使用它；否则使用默认值
      const customValue =
        defaultValue?.value || presets.find((item) => item.type === 'last-7');
      return {
        label: null,
        title: '自定义',
        timeType: 6,
        type: 'customDate',
        value: customValue,
      };
    }
    // 清空场景
    if (dateType === 'all') {
      return presets.find((item) => item.type === 'clear-time');
    }

    // 快捷默认时间
    return (
      presets.find((item) => item.type === dateType) ||
      presets.find((item) => item.type === 'last-7')
    );
  }, [defaultValue, presets]);

  const defaultDateItem = getDefaultDateItem();
  const [curDefaultValue, setCurDefaultValue] = React.useState(() => {
    // 如果 defaultValue 直接包含 value，优先使用
    if (defaultDateItem?.type === 'customDate') {
      return defaultDateItem?.value;
    }
    console.log(
      '4444555',
      presetList.find((item: any) => item.type === defaultValue?.type)?.value,
      defaultValue,
    );
    // 否则从预设列表中查找
    return (
      presetList.find((item: any) => item.type === defaultDateItem?.type)
        ?.value || presetList[0].value
    );
  });

  const curTimeType = useRef<any>(
    presetList.find((item: any) => item.type === defaultValue?.type)
      ?.timeType || presetList[0].timeType,
  );
  let selectNum = 0,
    changeNum = 0;
  const onSelect = (
    dateFilterRelativeUnit?: number,
    type?: string,
    handleChange?: any,
  ) => {
    selectNum += 1;
    // 预设范围选中回调，会在onChange之前调用
    curTimeType.current = dateFilterRelativeUnit;
    if (type === 'clear') {
      setCurDefaultValue([]);
      // handleChange([], curTimeType.current);
      setKey(Date.now());
    }
  };
  const presetsFun = getOtherPresets(onSelect);
  // 配置项变更手动重置为当前默认值，并刷新图表数据
  const handleDateChange = useCallback(
    (val, newField) => {
      console.log(
        val,
        newField,
        'daterange改变了----333',
        dateRange,
        dateRangeRef.current,
        '====',
        defaultValue,
      );
      if (dateRangeRef.current !== dateRange) {
        dateRangeRef.current = dateRange;
        setCurDefaultValue(getDefaultDateItem()?.value || curDefaultValue);
        setKey(Date.now());
      }
      if (defaultValue?.dateType === 'customDate') {
        defaultValueRef.current = defaultValue;
        temp_dateMap[filterId] = {
          value:
            defaultValue?.value ||
            presets.find((item) => item.type === 'last-7'),
          type: 6, // CUSTOM 类型
        };
        setKey(Date.now());
      } else if (
        JSON.stringify(defaultValueRef.current) !== JSON.stringify(defaultValue)
      ) {
        defaultValueRef.current = defaultValue;
        const newDefaultValue = getDefaultDateItem()?.value;
        setCurDefaultValue(newDefaultValue);
        temp_dateMap[filterId] = null;
        setKey(Date.now());
      }
    },
    [dateRange, defaultValue, curDefaultValue, filterId, presets],
  );

  return (
    <FilterWrapper
      onChange={handleDateChange}
      componentProps={{
        dataSetConfig,
        __id,
        componentId,
        defaultValue,
        dateRange,
        uuid,
      }}
      defaultValue={[
        dayjs(defaultDateItem?.value[0])
          .startOf('day')
          .valueOf(),
        dayjs(defaultDateItem?.value[1])
          .endOf('day')
          .valueOf(),
      ]}
      // value={[1751904000000, 1752767999999]}
      filterExtendField={(field) => {
        console.log(field, 'fieldItem----3333', defaultDateItem, defaultValue);
        return {
          fieldLabel: field.fieldValue?.length
            ? [
                dayjs(field.fieldValue[0]).startOf('day').format('YYYY-MM-DD') +
                  ' ~ ' +
                  dayjs(field.fieldValue[1]).endOf('day').format('YYYY-MM-DD'),
              ]
            : [],
          // dateFilterRelativeUnit: this.dateFilterRelativeUnit,
        };
      }}
      // value={curDefaultValue}
    >
      {({ value, onChange, fieldItem }) => {
        let valueChange = [];
        if (value && value.length > 0) {
          valueChange = [dayjs(value[0]).toDate(), dayjs(value[1]).toDate()];
        } else {
          valueChange = curDefaultValue;
        }
        console.log(
          value,
          valueChange,
          fieldItem,
          'fieldItem----1111',
          curDefaultValue,
        );

        return (
          <div className="lego-date-picker-filter">
            <DateRangePicker
              // @ts-ignore
              key={key}
              isMobile={isMobile()}
              onChange={(dateNum) => {
                console.log(dateNum, 'dateNum-----11111');
                onChange([
                  dayjs(dateNum[0]).startOf('day').valueOf(),
                  dayjs(dateNum[1]).endOf('day').valueOf(),
                ]);
              }}
              value={valueChange}
              presets={presetsFun}
              min={new Date('2021/01/01')}
              max={dayjs().toDate()}
              // 最多可选天数，根据配置动态设置
              maxStep={dateRange - 1}
              defaultValue={curDefaultValue}
              allowClear={false}
              placement="bottomLeft"
            />
          </div>
        );
      }}
    </FilterWrapper>
  );
};

DatePickerFilter.displayName = '时间筛选器';
