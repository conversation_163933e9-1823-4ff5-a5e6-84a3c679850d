@border-color: #E7E8EB;
.lego-filter-wrap {
  border-radius: 4px;
  width: 100%;
  color: rgba(0, 0, 0, 0.3);
  background: rgba(37, 52, 79, 0.03);
  font-size: 13px;
  font-weight: normal;
  line-height: 20px;
  display: flex;
  flex-direction: column;
  padding: 5px 10px;
}

.lego-filter-label-box{
  display: flex;
  width: 100%;
  .lego-filter-label{
    font-size: 14px;
    font-weight: normal;
    color: rgba(0, 0, 0, 0.6);
    padding: 5px 12px;
    max-width: 130px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
    flex: none;
    line-height: 20px;
    border: 1px solid @border-color;
    border-radius: 6px;
    background-color: #fff;
    border-inline-end: 0;
    border-start-end-radius: 0;
    border-end-end-radius: 0;
  }
  .lego-filter-label-wrap{
    flex: 1;
    >*{
      border-start-start-radius: 0;
      border-end-start-radius: 0;
    }
    .ant-select-selector{
      border-start-start-radius: 0;
      border-end-start-radius: 0;
    }
  }
}