import { Input } from '@blmcp/web-ui';
import FilterWrapper from '../wrapper/FilterWrapper';
import { DataSetConfig } from '../../type';

interface InputProps {
  dataSetConfig?: DataSetConfig;
  __id?: string; // 预览模式
  componentId?: string;
  __designMode?: string; // 编辑模式
  disabled?: boolean; // 禁用
  defaultValue?: string[];
}

// 关联组件参数与 setter 组件
export const LinkSetterComponent = {
  dataSetConfig: {
    // 用到的组件
    componentName: 'AnalysisConfig',
    // 该组件其他配置项
    props: {
      numDisabled: true,
      indexDisabled: true,
      dateDisabled: true,
      list: [
        {
          ignoreSetDefaultComputeType: true,
          disabledSortConfig: true,
          label: '字段',
          key: 'dimensionInfo',
          onlyOne: true,
          placeholder: '仅支持拖入 1 个字段',
        },
      ],
    },
  },
  // 是否隐藏
  hidden: {
    componentName: 'HiddenSetter',
    // 该组件其他配置项
    props: {
      noPadding: true,
    },
  },
};

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 4,
  dataType: 401,
};

export const InputFilter = function (props: InputProps) {
  return (
    <FilterWrapper
      label={true}
      componentProps={props}
      filterKey="dimensionInfo"
      filterExtendField={(field) => {
        return {
          symbol: field?.dataType === 0 ? 'LIKE' : 'EQUALS',
          fieldValue: field?.fieldValue.map((v) => v.trim()) || [],
        };
      }}
      handleInject={({ fieldItem }) => {
        return {
          placeholder: fieldItem.title || '请选择字段',
        };
      }}
    >
      <Input />
    </FilterWrapper>
  );
};
InputFilter.displayName = '文本筛选器';
