import { BLMFleetSelect } from '@blmcp/peento-businessComponents';
import { useRef, useMemo } from 'react';
import { useDispatch } from '@/utils/store';
import FilterWrapper from '../wrapper/FilterWrapper';
import stateContext, { StateProps } from '../../context/filterContext';

interface CityFilterProps {
  __id?: string; // 预览模式
  componentId?: string; // 编辑模式
  link?: string;
  uuid: string;
}

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 4,
  // 不参与获取数据集相关事情
  notQueryData: true,
};

// 关联组件参数与 setter 组件
export const LinkSetterComponent = {
  // 是否隐藏
  hidden: {
    componentName: 'HiddenSetter',
    // 该组件其他配置项
    props: {},
  },
};

export const NewFleetFilter = function (props: CityFilterProps) {
  const context = stateContext(props.uuid);
  const [state] = useDispatch<StateProps>(context);
  const ref = useRef(null);

  return (
    <FilterWrapper
      storeField="fleet"
      componentProps={props}
      fieldProps={useMemo(() => {
        return {
          key: 'fleet_id',
          columnId: *********,
          dataType: 0,
        };
      }, [])}
      handleFieldLabel={(val) => {
        return val.length ? ref.current?.getSelectedLabelName?.() : [];
      }}
      filterIdBK="fleet_id"
    >
      <BLMFleetSelect
        ref={ref}
        multiple
        authType={2} // 有权限的
        fleetStatus={2} // 不限制状态（启用+删除）
        tenantId={state.tenant}
        adcodeList={state.city}
        transportCompanyIdList={state.carTeam}
        openUaFlag
        maxLength={400}
        // showAll={true}
      />
    </FilterWrapper>
  );
};
