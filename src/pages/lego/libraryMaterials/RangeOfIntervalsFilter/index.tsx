import { RangeOfIntervals } from '@blmcp/web-ui';
import FilterWrapper from '../wrapper/FilterWrapper';
import { DataSetConfig } from '../../type';

interface RangeOfIntervalsProps {
  dataSetConfig?: DataSetConfig;
  __id?: string; // 预览模式
  componentId?: string;
  __designMode?: string; // 编辑模式
  disabled?: boolean; // 禁用
  defaultValue?: string[];
}

// 关联组件参数与 setter 组件
export const LinkSetterComponent = {
  dataSetConfig: {
    // 用到的组件
    componentName: 'AnalysisConfig',
    // 该组件其他配置项
    props: {
      list: [
        {
          ignoreSetDefaultComputeType: true,
          disabledSortConfig: true,
          label: '指标',
          key: 'measureInfo',
          onlyOne: true,
          placeholder: '仅支持拖入 1 个字段',
        },
      ],
      dimDisabled: true,
      aggDisabled: true,
      textDisabled: true,
    },
  },
  // 是否隐藏
  hidden: {
    componentName: 'HiddenSetter',
    // 该组件其他配置项
    props: {},
  },
};

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 4,
  dataType: 402,
};

const isNoEmpty = (value: any) => {
  return value !== undefined && value !== null;
};

export const RangeOfIntervalsFilter = function (props: RangeOfIntervalsProps) {
  return (
    <FilterWrapper
      label={true}
      componentProps={props}
      filterKey="measureInfo"
      filterExtendField={(field) => {
        const handleValue = field.fieldValue.filter((f) => isNoEmpty(f));
        const _symbol =
          (isNoEmpty(field.fieldValue[0]) &&
            isNoEmpty(field.fieldValue[1]) &&
            'BETWEEN') ||
          (isNoEmpty(field.fieldValue[0]) && 'GTE') ||
          'LTE';
        let handleLabel: string[] = [];
        if (!handleValue.length) {
          handleLabel = [];
        } else if (_symbol === 'BETWEEN') {
          handleLabel = [handleValue.join(' - ')];
        } else if (_symbol === 'GTE') {
          handleLabel = ['≥' + field.fieldValue[0]];
        } else if (_symbol === 'LTE') {
          handleLabel = ['≤' + field.fieldValue[1]];
        }
        return {
          symbol: _symbol,
          fieldValue: handleValue,
          fieldLabel: handleLabel,
        };
      }}
    >
      <RangeOfIntervals></RangeOfIntervals>
    </FilterWrapper>
  );
};
RangeOfIntervalsFilter.displayName = '区间筛选器';
